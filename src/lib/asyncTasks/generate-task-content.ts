import { generateTaskContent } from '../services/generate-task-content.js';

export async function generateTaskContentAsync(sql: any, {
  campaignId,
  companyId,
}: {
  campaignId: string;
  companyId: string;
}) {
  try {
    console.log('Starting async task content generation for campaign:', campaignId);

    // Fetch campaign information
    const campaignResult = await sql`
      SELECT objective, products, external_research, target_icps, target_personas 
      FROM company_campaigns 
      WHERE id = ${campaignId}
    `;
    
    if (campaignResult.length === 0) {
      throw new Error(`Campaign not found: ${campaignId}`);
    }
    
    const campaign = campaignResult[0];

    // Fetch all tasks for this campaign that need content generation
    const tasksResult = await sql`
      SELECT id, task_title, task_description, content_type, channel, is_generating, error_generating
      FROM company_content
      WHERE campaign_id = ${campaignId}
        AND company_id = ${companyId}
        AND (content IS NULL OR content = '')
        AND is_generating = false
        AND (error_generating IS NULL OR error_generating = false)
    `;

    console.log(`Found ${tasksResult.length} tasks needing content generation`);

    if (tasksResult.length === 0) {
      console.log('No tasks found that need content generation');
      return;
    }

    // Fetch supporting data for content generation
    let productInformation = '';
    if (campaign.products && campaign.products.length > 0) {
      const productsResult = await sql`
        SELECT name, description, key_features, target_audience FROM products 
        WHERE id = ANY(${campaign.products}) AND company_id = ${companyId}
      `;
      productInformation = productsResult.map(product => {
        const features = Array.isArray(product.key_features) 
          ? product.key_features.map(f => `${f.name}: ${f.value_prop} (${f.differentiation})`).join(', ')
          : '';
        const targetAudience = Array.isArray(product.target_audience) 
          ? product.target_audience.join(', ')
          : '';
        return `Product: ${product.name}\nDescription: ${product.description || 'N/A'}\nKey Features: ${features}\nTarget Audience: ${targetAudience}`;
      }).join('\n\n');
    }

    // Fetch external research
    let externalResearchContent = '';
    if (campaign.external_research && campaign.external_research.length > 0) {
      const researchResult = await sql`
        SELECT description FROM saved_research 
        WHERE id = ANY(${campaign.external_research}) AND account_id = ${companyId}
      `;
      externalResearchContent = researchResult
        .map(research => JSON.stringify(research.description))
        .join('\n\n');
    }

    // Fetch company brand information
    let companyBrand = {};
    try {
      const brandResult = await sql`
        SELECT brand_profile, messaging_strategy, visual_identity, product_catalog 
        FROM company_brand 
        WHERE company_id = ${companyId}
      `;
      if (brandResult.length > 0) {
        companyBrand = brandResult[0];
      }
    } catch (error) {
      console.log('No company brand found, continuing without brand info');
    }

    // Fetch account data as fallback
    const accountResult = await sql`
      SELECT name, public_data FROM accounts WHERE id = ${companyId}
    `;
    const accountData = accountResult[0];
    const contextData = accountData?.public_data?.cleanedText || '';

    // Fetch target ICPs
    let targetICPs = '';
    if (campaign.target_icps && campaign.target_icps.length > 0) {
      const icpsResult = await sql`
        SELECT data FROM icps 
        WHERE id = ANY(${campaign.target_icps})
      `;
      targetICPs = icpsResult
        .map(icp => JSON.stringify(icp.data))
        .join('\n\n');
    }

    // Fetch target personas
    let targetPersonas = '';
    if (campaign.target_personas && campaign.target_personas.length > 0) {
      const personasResult = await sql`
        SELECT data FROM personas 
        WHERE id = ANY(${campaign.target_personas})
      `;
      targetPersonas = personasResult
        .map(persona => JSON.stringify(persona.data))
        .join('\n\n');
    }

    // Generate content for each task
    for (const task of tasksResult) {
      try {
        console.log(`Generating content for task: ${task.task_title}`);
        
        // Mark task as generating
        await sql`
          UPDATE company_content
          SET is_generating = true, error_generating = false, updated_at = ${Date.now()}
          WHERE id = ${task.id}
        `;

        const contentParams = {
          taskTitle: task.task_title,
          taskDescription: task.task_description,
          contentType: task.content_type,
          channel: task.channel,
          campaignGoal: campaign.objective,
          productInformation: productInformation || contextData,
          targetICPs: targetICPs,
          targetPersonas: targetPersonas,
          companyBrand: companyBrand,
          externalResearch: externalResearchContent,
        };

        const generatedContent = await generateTaskContent(contentParams);

        // Update task with generated content
        await sql`
          UPDATE company_content 
          SET 
            content = ${generatedContent.content},
            visual_description = ${generatedContent.visual_description || null},
            seo_keywords = ${generatedContent.seo_keywords ? sql.json(generatedContent.seo_keywords) : null},
            trend_keywords = ${generatedContent.trend_keywords ? sql.json(generatedContent.trend_keywords) : null},
            is_generating = false,
            updated_at = ${Date.now()}
          WHERE id = ${task.id}
        `;

        console.log(`✅ Content generated for task: ${task.task_title}`);
        
      } catch (error) {
        console.error(`Failed to generate content for task ${task.task_title}:`, error);
        
        // Mark task as failed
        await sql`
          UPDATE company_content
          SET
            is_generating = false,
            error_generating = true,
            updated_at = ${Date.now()}
          WHERE id = ${task.id}
        `;
      }
    }

    console.log('✅ Task content generation completed for campaign:', campaignId);

  } catch (error) {
    console.error('Failed to generate task content:', error);

    // Mark any remaining generating tasks as failed
    try {
      await sql`
        UPDATE company_content
        SET
          is_generating = false,
          error_generating = true,
          updated_at = ${Date.now()}
        WHERE campaign_id = ${campaignId}
          AND company_id = ${companyId}
          AND is_generating = true
      `;
    } catch (updateError) {
      console.error('Failed to update failed tasks:', updateError);
    }

    throw error;
  }
}

// Helper function to get content generation status for a campaign
export async function getContentGenerationStatus(sql: any, campaignId: string): Promise<{
  total: number;
  completed: number;
  generating: number;
  failed: number;
  pending: number;
}> {
  const statusResult = await sql`
    SELECT
      COUNT(*) as total,
      COUNT(CASE WHEN content IS NOT NULL AND content != '' THEN 1 END) as completed,
      COUNT(CASE WHEN is_generating = true THEN 1 END) as generating,
      COUNT(CASE WHEN error_generating = true THEN 1 END) as failed,
      COUNT(CASE WHEN (content IS NULL OR content = '') AND is_generating = false AND (error_generating IS NULL OR error_generating = false) THEN 1 END) as pending
    FROM company_content
    WHERE campaign_id = ${campaignId}
  `;

  return statusResult[0] || { total: 0, completed: 0, generating: 0, failed: 0, pending: 0 };
}
