import postgres from 'postgres';
import { generateContentSchedule } from '../services/generate-schedule.js';


export async function companyCampaignsInsert(sql: any, {
  id,
  company_id,
  products,
  external_research,
  target_icps,
  target_personas,
  objective,
  start_date,
  end_date,
  templateId,
}: {
  id: string;
  company_id: string;
  products: string[];
  external_research: string[];
  target_icps: string[];
  target_personas: string[];
  objective: string;
  start_date: string;
  end_date: string;
  templateId: string;
}) {

try {
  
  // Fetch product information
  let productInformation = '';

  if (products && products.length > 0) {
    const productsResult = await sql`
      SELECT name, description, key_features, target_audience FROM products 
      WHERE id = ANY(${products}) AND company_id = ${company_id}
    `;
    productInformation = productsResult.map(product => {
      const features = Array.isArray(product.key_features) 
        ? product.key_features.map(f => `${f.name}: ${f.value_prop} (${f.differentiation})`).join(', ')
        : '';
      const targetAudience = Array.isArray(product.target_audience) 
        ? product.target_audience.join(', ')
        : '';
      return `Product: ${product.name}\nDescription: ${product.description || 'N/A'}\nKey Features: ${features}\nTarget Audience: ${targetAudience}`;
    }).join('\n\n');
  }
  // Fetch external research
  let externalResearchContent = '';
  if (external_research && external_research.length > 0) {
    const researchResult = await sql`
      SELECT description FROM saved_research 
      WHERE id = ANY(${external_research}) AND account_id = ${company_id}
    `;
    externalResearchContent = researchResult
      .map(research => JSON.stringify(research.description))
      .join('\n\n');
  }
  // fetch company brand if exists
  const companyBrandResult = await sql`
    SELECT * FROM company_brand WHERE company_id = ${company_id}
  `;
  console.log('companyBrandResult', companyBrandResult);
  const companyBrand = companyBrandResult[0];
  // extract brand_profile, messaging_strategy, product_catalog, prompt_library, visual_identity
  // const brandProfile = companyBrand?.brand_profile || '';
  // const messagingStrategy = companyBrand?.messaging_strategy || '';
  // const productCatalog = companyBrand?.product_catalog || '';
  // const promptLibrary = companyBrand?.prompt_library || '';
  // const visualIdentity = companyBrand?.visual_identity || '';
  // Fetch website information for additional context
  const accountResult = await sql`
    SELECT name, public_data FROM accounts WHERE id = ${company_id}
  `;
  // TODO: what happens if there is no account data? 
  const accountData = accountResult[0];
  const contextData = accountData?.public_data?.cleanedText || '';

  // Fetch target ICPs
  let targetICPs = '';
  if (target_icps && target_icps.length > 0) {
    const icpsResult = await sql`
      SELECT data FROM icps 
      WHERE id = ANY(${target_icps})
    `;
    targetICPs = icpsResult
      .map(icp => JSON.stringify(icp.data))
      .join('\n\n');
  }

  // Fetch target personas
  let targetPersonas = '';
  if (target_personas && target_personas.length > 0) {
    const personasResult = await sql`
      SELECT data FROM personas 
      WHERE id = ANY(${target_personas})
    `;
    targetPersonas = personasResult
      .map(persona => JSON.stringify(persona.data))
      .join('\n\n');
  }
  console.log('targetICPs', targetICPs);
  console.log('targetPersonas', targetPersonas);
    // Generate content schedule
  const scheduleParams = {
    productDocumentation: productInformation || contextData,
    campaignGoal: objective,
    startDate: start_date,
    endDate: end_date,
    externalResearch: externalResearchContent,
    targetICPs: targetICPs,
    targetPersonas: targetPersonas,
    companyBrand: companyBrand,
  };

  const schedule = await generateContentSchedule(scheduleParams);

  // Insert content items into company_content table
  for (const item of schedule) {
    // Convert scheduled time to timestamp
    // const scheduledTime = new Date(`${item.scheduled_publishing_time.date}T${item.scheduled_publishing_time.time}:00`).getTime();
    console.log('item', JSON.stringify(item));
    await sql`
      INSERT INTO company_content (
        id, campaign_id, company_id, content_type, language, task_title, task_description, 
        channel, scheduled_publishing_time,
        is_draft, is_generating, created_at, updated_at
      ) VALUES (
        gen_random_uuid(),
        ${id},
        ${company_id},
        ${item.content_type},
        ${item.language},
        ${item.title},
        ${item.content_draft.description},
        ${item.channel},
        ${item.scheduled_publishing_time.toString()},
        true,
        false,
        ${Date.now()},
        ${Date.now()}
      )
    `;
  }

  // Update campaign to indicate content generation is complete
  await sql`
    UPDATE company_campaigns 
    SET 
      is_generating = false,
      error_generating = false,
      metadata = ${JSON.stringify({ 
        templateId,
      })}
    WHERE id = ${id}
  `;
} catch (error) {
  console.error('Failed to generate content schedule:', error);
  
  // Update the campaign to indicate failure
  await sql`
    UPDATE company_campaigns 
    SET 
      is_generating = false,
      error_generating = true,
      updated_at = ${Date.now()}
    WHERE id = ${id}
  `;
}
}