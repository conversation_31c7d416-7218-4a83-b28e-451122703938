import dotenv from 'dotenv';
import { Langfuse } from "langfuse";
import { callLLM } from '../utils/callLLM.js';

dotenv.config();

const langfuse = new Langfuse({
  secretKey: process.env.LANGFUSE_SECRET_KEY,
  publicKey: process.env.LANGFUSE_PUBLIC_KEY,
  baseUrl: process.env.LANGFUSE_BASE_URL
});

interface TaskContentParams {
  taskTitle: string;
  taskDescription: string;
  contentType: string;
  channel: string;
  campaignGoal: string;
  productInformation?: string;
  targetICPs?: string;
  targetPersonas?: string;
  companyBrand?: object;
  externalResearch?: string;
}

interface GeneratedTaskContent {
  content: string;
  visual_description?: string;
  seo_keywords?: string[];
  trend_keywords?: string[];
}

export async function generateTaskContent(params: TaskContentParams): Promise<GeneratedTaskContent> {
  try {
    const { 
      taskTitle, 
      taskDescription, 
      contentType, 
      channel, 
      campaignGoal, 
      productInformation, 
      targetICPs, 
      targetPersonas, 
      companyBrand,
      externalResearch 
    } = params;

    console.log("Generating content for task:", taskTitle);

    try {
      // Try to get the dedicated task content generation prompt
      const prompt = await langfuse.getPrompt("generat_content_body_v2", undefined, { label: "latest" })
      const compiledPrompt = prompt.compile({
        task_title: taskTitle,
        task_description: taskDescription,
        content_type: contentType,
        channel: channel,
        campaign_goal: campaignGoal,
        product_information: productInformation || "",
        target_icps: targetICPs || "",
        target_personas: targetPersonas || "",
        brand_book: JSON.stringify(companyBrand) || "",
        external_research: externalResearch || "",
      });

      console.log("Compiled prompt for task content generation:", compiledPrompt);
      const response = await callLLM(compiledPrompt, prompt, "google/gemini-2.5-pro-preview");
      return response;
    } catch (promptError) {
      console.warn("generate_task_content prompt not found, using fallback approach:", promptError);

      // Fallback: Create a simple content generation using direct LLM call
      const fallbackPrompt = `Generate marketing content for the following task:

Task Title: ${taskTitle}
Task Description: ${taskDescription}
Content Type: ${contentType}
Channel: ${channel}
Campaign Goal: ${campaignGoal}

${productInformation ? `Product Information:\n${productInformation}\n` : ''}
${targetICPs ? `Target ICPs:\n${targetICPs}\n` : ''}
${targetPersonas ? `Target Personas:\n${targetPersonas}\n` : ''}
${companyBrand ? `Brand Information:\n${JSON.stringify(companyBrand)}\n` : ''}
${externalResearch ? `External Research:\n${externalResearch}\n` : ''}

Please generate engaging, high-quality content that aligns with the campaign goal and is appropriate for the specified channel and content type. Return the response in JSON format with the following structure:
{
  "content": "The main content text",
  "visual_description": "Description for any visual elements (optional)",
  "seo_keywords": ["keyword1", "keyword2"],
  "trend_keywords": ["trend1", "trend2"]
}`;

      // Use a basic LLM call without Langfuse prompt
      const response = await callLLM(fallbackPrompt, { temperature: 0.7, schema: { type: "json_object" } }, "google/gemini-2.5-pro-preview");
      return response;
    }
    
  } catch (error) {
    console.error('Error generating task content:', error);
    throw new Error('Failed to generate task content. Please check your API key and try again.');
  }
}
