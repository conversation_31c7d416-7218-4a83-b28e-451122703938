import { Block } from "@blocknote/core";

export interface CompanyContent {
  readonly id: string;
  readonly campaign_id: string;
  readonly idea_id: string;
  readonly content_type?: string;
  readonly language: string;
  readonly content: string;
  readonly created_at?: string;
  readonly updated_at?: string;
  readonly image_path?: string;
  readonly content_template?: string;
  readonly image_url?: string;
  readonly channel?: string;
  readonly has_image?: boolean;
  readonly visual_description?: string;
  readonly visual_description_group?: string;
  readonly avatar_script?: string;
  readonly avatar_voice_id?: string;
  readonly avatar_presenter_id?: string;
  readonly avatar_video_id?: string;
  readonly avatar_video_url?: string;
  readonly is_avatar_ready?: boolean;
  readonly has_avatar?: boolean;
  readonly is_generating?: boolean;
  readonly error_generating?: boolean;
  readonly status?: string;
  readonly task_description?: string;
  readonly task_id?: string;
  readonly task_title?: string;
  readonly video_presentation_url?: string;
  readonly has_video_presentation?: boolean;
  readonly image_urls?: string[];
  readonly video_presentation_script?: string;
  readonly video_presentation_render_params?: {
    [key: string]: any;
  };
  readonly company_id: string;
  readonly seo_keywords?: {
    [key: string]: any;
  };
  readonly trend_keywords?: string[];
  readonly is_draft?: boolean;
  readonly scheduled_publishing_time?: string | null;
  readonly content_editor_template?: Block[] | null;
  // Publishing related fields
  readonly is_published?: boolean;
  readonly published_at?: number;
  readonly published_by?: string;
  readonly published_url?: string;
  // Scheduling related fields
  readonly is_scheduled?: boolean;
  readonly scheduled_at?: number;
  readonly schedule_date?: string;
  readonly ayrshare_post_id?: string;
  readonly is_paused?: boolean;
  // Video editor related fields
  readonly video_editor_overlays?: any[];
  readonly video_editor_aspect_ratio?: { width: number; height: number };
  readonly video_editor_player_dimensions?: { width: number; height: number };
  readonly assigned_to?: string;
}
