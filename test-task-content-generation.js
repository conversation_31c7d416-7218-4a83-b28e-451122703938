import { generateTaskContent } from './src/lib/services/generate-task-content.js';

async function testTaskContentGeneration() {
  try {
    console.log('Testing task content generation...');
    
    const testParams = {
      taskTitle: "Create LinkedIn Post about Product Launch",
      taskDescription: "Create an engaging LinkedIn post announcing our new AI-powered analytics platform",
      contentType: "social_media_post",
      channel: "linkedin",
      campaignGoal: "Increase brand awareness and drive product signups",
      productInformation: "AI Analytics Platform: Advanced analytics tool that helps businesses make data-driven decisions with AI-powered insights",
      targetICPs: "Tech companies with 50-500 employees",
      targetPersonas: "Data analysts, business intelligence professionals, CTOs",
      companyBrand: {
        mission: "Empowering businesses with AI-driven insights",
        tone: "Professional yet approachable",
        values: ["Innovation", "Data-driven decisions", "Customer success"]
      },
      externalResearch: "Market research shows 73% of companies struggle with data analysis"
    };

    const result = await generateTaskContent(testParams);
    
    console.log('✅ Task content generation successful!');
    console.log('Generated content:', JSON.stringify(result, null, 2));
    
  } catch (error) {
    console.error('❌ Task content generation failed:', error);
  }
}

testTaskContentGeneration();
